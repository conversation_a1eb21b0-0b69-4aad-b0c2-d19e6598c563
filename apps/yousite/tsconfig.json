{"extends": "@repo/typescript-config/base.json", "compilerOptions": {"jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/cms/*": ["./src/cms/*"], "@repo/ui/*": ["../../packages/ui/src/*"], "@repo/ui-business-editor/*": ["../../packages/ui-business-editor/src/*"], "@i18n/*": ["./src/i18n/*"]}, "allowJs": true, "noEmit": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next"]}